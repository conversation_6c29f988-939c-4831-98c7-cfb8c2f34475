Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    main.o(i.main) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to led.o(i.LED_On) for LED_On
    main.o(i.main) refers to pm25.o(i.PM25_Init) for PM25_Init
    main.o(i.main) refers to usart.o(i.USART1_Config) for USART1_Config
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to printf1.o(i.__0printf$1) for __2printf
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    main.o(i.main) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    main.o(i.main) refers to led.o(i.LED_Toggle) for LED_Toggle
    main.o(i.main) refers to pm25.o(i.PM25_GetData) for PM25_GetData
    main.o(i.main) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.main) refers to main.o(.data) for pm
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Off) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_On) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Toggle) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED_Toggle) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    led.o(i.LED_Twinkle) refers to led.o(i.LED_On) for LED_On
    led.o(i.LED_Twinkle) refers to delay.o(i.delay_ms) for delay_ms
    led.o(i.LED_Twinkle) refers to led.o(i.LED_Off) for LED_Off
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_I2C_WaitAck) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_I2C_WaitAck) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_Send_Byte) for OLED_Send_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_I2C_WaitAck) for OLED_I2C_WaitAck
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Send_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Send_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_0806
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowChinese) refers to oled.o(.constdata) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_Send_Byte) for OLED_Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_I2C_WaitAck) for OLED_I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    pm25.o(i.Get_PM25_Average_Data) refers to pm25.o(i.PM25_GetData) for PM25_GetData
    pm25.o(i.Get_PM25_Average_Data) refers to delay.o(i.delay_ms) for delay_ms
    pm25.o(i.PM25_ADC_Read) refers to adcx.o(i.ADC_GetValue) for ADC_GetValue
    pm25.o(i.PM25_GetData) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    pm25.o(i.PM25_GetData) refers to delay.o(i.delay_us) for delay_us
    pm25.o(i.PM25_GetData) refers to pm25.o(i.PM25_ADC_Read) for PM25_ADC_Read
    pm25.o(i.PM25_GetData) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    pm25.o(i.PM25_GetData) refers to ffltui.o(.text) for __aeabi_ui2f
    pm25.o(i.PM25_GetData) refers to fmul.o(.text) for __aeabi_fmul
    pm25.o(i.PM25_GetData) refers to fdiv.o(.text) for __aeabi_fdiv
    pm25.o(i.PM25_GetData) refers to f2d.o(.text) for __aeabi_f2d
    pm25.o(i.PM25_GetData) refers to dmul.o(.text) for __aeabi_dmul
    pm25.o(i.PM25_GetData) refers to dadd.o(.text) for __aeabi_dsub
    pm25.o(i.PM25_GetData) refers to dfixi.o(.text) for __aeabi_d2iz
    pm25.o(i.PM25_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pm25.o(i.PM25_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pm25.o(i.PM25_Init) refers to adcx.o(i.ADCx_Init) for ADCx_Init
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    usart.o(i.USART1_Config) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Config) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART1_Config) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Config) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    adcx.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adcx.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adcx.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adcx.o(i.ADC_GetValue) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adcx.o(i.ADCx_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adcx.o(i.ADCx_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adcx.o(i.ADCx_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adcx.o(i.ADCx_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adcx.o(i.ADCx_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adcx.o(i.ADCx_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adcx.o(i.ADCx_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adcx.o(i.ADCx_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing led.o(i.LED_Off), (20 bytes).
    Removing led.o(i.LED_Twinkle), (18 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (168 bytes).
    Removing oled.o(i.OLED_DrawLine), (172 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (172 bytes).
    Removing oled.o(i.OLED_ShowPicture), (218 bytes).
    Removing oled.o(i.OLED_ShowString), (78 bytes).
    Removing pm25.o(i.Get_PM25_Average_Data), (42 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_md.o(HEAP), (0 bytes).
    Removing misc.o(i.NVIC_Init), (112 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

121 unused section(s) (total 4622 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_md.s           0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\OLED\oled.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\PM25\PM25.c                  0x00000000   Number         0  pm25.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\adc\adcx.c                     0x00000000   Number         0  adcx.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000100   Section       28  startup_stm32f10x_md.o(.text)
    .text                                    0x0800011c   Section        0  fmul.o(.text)
    .text                                    0x08000180   Section        0  fdiv.o(.text)
    .text                                    0x080001fc   Section        0  dadd.o(.text)
    .text                                    0x0800034a   Section        0  dmul.o(.text)
    .text                                    0x0800042e   Section        0  ffltui.o(.text)
    .text                                    0x08000438   Section        0  dfixi.o(.text)
    .text                                    0x08000476   Section        0  f2d.o(.text)
    .text                                    0x0800049c   Section        0  uidiv.o(.text)
    .text                                    0x080004c8   Section        0  llshl.o(.text)
    .text                                    0x080004e6   Section        0  llushr.o(.text)
    .text                                    0x08000506   Section        0  llsshr.o(.text)
    .text                                    0x0800052a   Section        0  iusefp.o(.text)
    .text                                    0x0800052a   Section        0  fepilogue.o(.text)
    .text                                    0x08000598   Section        0  depilogue.o(.text)
    .text                                    0x08000654   Section       36  init.o(.text)
    i.ADC_Cmd                                0x08000678   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_GetCalibrationStatus               0x0800068e   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetConversionValue                 0x080006a2   Section        0  stm32f10x_adc.o(i.ADC_GetConversionValue)
    i.ADC_GetFlagStatus                      0x080006aa   Section        0  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    i.ADC_GetResetCalibrationStatus          0x080006bc   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_GetValue                           0x080006d0   Section        0  adcx.o(i.ADC_GetValue)
    i.ADC_Init                               0x08000704   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x08000754   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x0800080c   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x08000816   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x0800082c   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.ADCx_Init                              0x08000838   Section        0  adcx.o(i.ADCx_Init)
    i.BusFault_Handler                       0x080008a4   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080008a8   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080008aa   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadOutputDataBit                 0x080009c0   Section        0  stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit)
    i.GPIO_ResetBits                         0x080009d2   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080009d6   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x080009da   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x080009e4   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.IIC_delay                              0x080009e8   Section        0  oled.o(i.IIC_delay)
    i.LED_Init                               0x080009f8   Section        0  led.o(i.LED_Init)
    i.LED_On                                 0x08000a2c   Section        0  led.o(i.LED_On)
    i.LED_Toggle                             0x08000a40   Section        0  led.o(i.LED_Toggle)
    i.MemManage_Handler                      0x08000a64   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000a68   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08000a6c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_DrawPoint                         0x08000a9c   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_I2C_Start                         0x08000b14   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08000b50   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_I2C_WaitAck                       0x08000b7c   Section        0  oled.o(i.OLED_I2C_WaitAck)
    i.OLED_Init                              0x08000bb0   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08000cdc   Section        0  oled.o(i.OLED_Pow)
    i.OLED_Refresh                           0x08000cf4   Section        0  oled.o(i.OLED_Refresh)
    i.OLED_Send_Byte                         0x08000d5c   Section        0  oled.o(i.OLED_Send_Byte)
    i.OLED_ShowChar                          0x08000db0   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowChinese                       0x08000ef0   Section        0  oled.o(i.OLED_ShowChinese)
    i.OLED_ShowNum                           0x08000ff8   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_WR_Byte                           0x0800108c   Section        0  oled.o(i.OLED_WR_Byte)
    i.PM25_ADC_Read                          0x080010c4   Section        0  pm25.o(i.PM25_ADC_Read)
    i.PM25_GetData                           0x080010d0   Section        0  pm25.o(i.PM25_GetData)
    i.PM25_Init                              0x08001190   Section        0  pm25.o(i.PM25_Init)
    i.PendSV_Handler                         0x080011cc   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_ADCCLKConfig                       0x080011d0   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_APB2PeriphClockCmd                 0x080011e8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001208   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x080012dc   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080012de   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080012df   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080012e8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080012e9   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x080013c8   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x080013f0   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080013f4   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_Config                          0x08001454   Section        0  usart.o(i.USART1_Config)
    i.USART_Cmd                              0x080014d0   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_Init                             0x080014e8   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_SendData                         0x080015c0   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x080015c8   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__0printf$1                            0x080015cc   Section        0  printf1.o(i.__0printf$1)
    i.__scatterload_copy                     0x080015ec   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080015fa   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080015fc   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x0800160c   Section        0  printf1.o(i._printf_core)
    _printf_core                             0x0800160d   Thumb Code   336  printf1.o(i._printf_core)
    i.delay_init                             0x08001760   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800179c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080017d8   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x08001814   Section        0  usart.o(i.fputc)
    i.main                                   0x08001838   Section        0  main.o(i.main)
    .constdata                               0x0800190c   Section     7472  oled.o(.constdata)
    .data                                    0x20000000   Section        2  main.o(.data)
    .data                                    0x20000002   Section        4  delay.o(.data)
    fac_us                                   0x20000002   Data           1  delay.o(.data)
    fac_ms                                   0x20000004   Data           2  delay.o(.data)
    .data                                    0x20000006   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000006   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000016   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x2000001c   Section        4  stdout.o(.data)
    .bss                                     0x20000020   Section     1152  oled.o(.bss)
    STACK                                    0x200004a0   Section      512  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000101   Thumb Code     4  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x08000117   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __aeabi_fmul                             0x0800011d   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x08000181   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x080001fd   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800033f   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000345   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800034b   Thumb Code   228  dmul.o(.text)
    __aeabi_ui2f                             0x0800042f   Thumb Code    10  ffltui.o(.text)
    __aeabi_d2iz                             0x08000439   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x08000477   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x0800049d   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800049d   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080004c9   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080004c9   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080004e7   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080004e7   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000507   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000507   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0800052b   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800052b   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800053d   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000599   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080005b7   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x08000655   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000655   Thumb Code     0  init.o(.text)
    ADC_Cmd                                  0x08000679   Thumb Code    22  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_GetCalibrationStatus                 0x0800068f   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetConversionValue                   0x080006a3   Thumb Code     8  stm32f10x_adc.o(i.ADC_GetConversionValue)
    ADC_GetFlagStatus                        0x080006ab   Thumb Code    18  stm32f10x_adc.o(i.ADC_GetFlagStatus)
    ADC_GetResetCalibrationStatus            0x080006bd   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_GetValue                             0x080006d1   Thumb Code    48  adcx.o(i.ADC_GetValue)
    ADC_Init                                 0x08000705   Thumb Code    70  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x08000755   Thumb Code   184  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x0800080d   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x08000817   Thumb Code    22  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x0800082d   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    ADCx_Init                                0x08000839   Thumb Code   104  adcx.o(i.ADCx_Init)
    BusFault_Handler                         0x080008a5   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080008a9   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080008ab   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadOutputDataBit                   0x080009c1   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit)
    GPIO_ResetBits                           0x080009d3   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080009d7   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x080009db   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x080009e5   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    IIC_delay                                0x080009e9   Thumb Code    16  oled.o(i.IIC_delay)
    LED_Init                                 0x080009f9   Thumb Code    48  led.o(i.LED_Init)
    LED_On                                   0x08000a2d   Thumb Code    14  led.o(i.LED_On)
    LED_Toggle                               0x08000a41   Thumb Code    32  led.o(i.LED_Toggle)
    MemManage_Handler                        0x08000a65   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08000a69   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    OLED_Clear                               0x08000a6d   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_DrawPoint                           0x08000a9d   Thumb Code   114  oled.o(i.OLED_DrawPoint)
    OLED_I2C_Start                           0x08000b15   Thumb Code    56  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08000b51   Thumb Code    38  oled.o(i.OLED_I2C_Stop)
    OLED_I2C_WaitAck                         0x08000b7d   Thumb Code    46  oled.o(i.OLED_I2C_WaitAck)
    OLED_Init                                0x08000bb1   Thumb Code   294  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08000cdd   Thumb Code    22  oled.o(i.OLED_Pow)
    OLED_Refresh                             0x08000cf5   Thumb Code   100  oled.o(i.OLED_Refresh)
    OLED_Send_Byte                           0x08000d5d   Thumb Code    78  oled.o(i.OLED_Send_Byte)
    OLED_ShowChar                            0x08000db1   Thumb Code   304  oled.o(i.OLED_ShowChar)
    OLED_ShowChinese                         0x08000ef1   Thumb Code   248  oled.o(i.OLED_ShowChinese)
    OLED_ShowNum                             0x08000ff9   Thumb Code   148  oled.o(i.OLED_ShowNum)
    OLED_WR_Byte                             0x0800108d   Thumb Code    56  oled.o(i.OLED_WR_Byte)
    PM25_ADC_Read                            0x080010c5   Thumb Code    12  pm25.o(i.PM25_ADC_Read)
    PM25_GetData                             0x080010d1   Thumb Code   162  pm25.o(i.PM25_GetData)
    PM25_Init                                0x08001191   Thumb Code    56  pm25.o(i.PM25_Init)
    PendSV_Handler                           0x080011cd   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_ADCCLKConfig                         0x080011d1   Thumb Code    18  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_APB2PeriphClockCmd                   0x080011e9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001209   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x080012dd   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x080013c9   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x080013f1   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080013f5   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_Config                            0x08001455   Thumb Code   114  usart.o(i.USART1_Config)
    USART_Cmd                                0x080014d1   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_Init                               0x080014e9   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_SendData                           0x080015c1   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x080015c9   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __0printf$1                              0x080015cd   Thumb Code    22  printf1.o(i.__0printf$1)
    __1printf$1                              0x080015cd   Thumb Code     0  printf1.o(i.__0printf$1)
    __2printf                                0x080015cd   Thumb Code     0  printf1.o(i.__0printf$1)
    __scatterload_copy                       0x080015ed   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080015fb   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080015fd   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    delay_init                               0x08001761   Thumb Code    52  delay.o(i.delay_init)
    delay_ms                                 0x0800179d   Thumb Code    56  delay.o(i.delay_ms)
    delay_us                                 0x080017d9   Thumb Code    56  delay.o(i.delay_us)
    fputc                                    0x08001815   Thumb Code    32  usart.o(i.fputc)
    main                                     0x08001839   Thumb Code   182  main.o(i.main)
    asc2_0806                                0x0800190c   Data         552  oled.o(.constdata)
    asc2_1206                                0x08001b34   Data        1140  oled.o(.constdata)
    asc2_1608                                0x08001fa8   Data        1520  oled.o(.constdata)
    asc2_2412                                0x08002598   Data        3420  oled.o(.constdata)
    Hzk1                                     0x080032f4   Data         128  oled.o(.constdata)
    Hzk2                                     0x08003374   Data          72  oled.o(.constdata)
    Hzk3                                     0x080033bc   Data         128  oled.o(.constdata)
    Hzk4                                     0x0800343c   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x0800363c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800365c   Number         0  anon$$obj.o(Region$$Table)
    pm                                       0x20000000   Data           2  main.o(.data)
    __stdout                                 0x2000001c   Data           4  stdout.o(.data)
    OLED_GRAM                                0x20000020   Data        1152  oled.o(.bss)
    __initial_sp                             0x200006a0   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000367c, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000365c, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO          589    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         1346  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         1624    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         1627    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1629    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1631    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         1632    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         1634    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080000fc   0x080000fc   0x00000000   Code   RO         1636    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         1625    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000100   0x08000100   0x0000001c   Code   RO          590    .text               startup_stm32f10x_md.o
    0x0800011c   0x0800011c   0x00000064   Code   RO         1610    .text               mf_w.l(fmul.o)
    0x08000180   0x08000180   0x0000007c   Code   RO         1612    .text               mf_w.l(fdiv.o)
    0x080001fc   0x080001fc   0x0000014e   Code   RO         1614    .text               mf_w.l(dadd.o)
    0x0800034a   0x0800034a   0x000000e4   Code   RO         1616    .text               mf_w.l(dmul.o)
    0x0800042e   0x0800042e   0x0000000a   Code   RO         1618    .text               mf_w.l(ffltui.o)
    0x08000438   0x08000438   0x0000003e   Code   RO         1620    .text               mf_w.l(dfixi.o)
    0x08000476   0x08000476   0x00000026   Code   RO         1622    .text               mf_w.l(f2d.o)
    0x0800049c   0x0800049c   0x0000002c   Code   RO         1639    .text               mc_w.l(uidiv.o)
    0x080004c8   0x080004c8   0x0000001e   Code   RO         1643    .text               mc_w.l(llshl.o)
    0x080004e6   0x080004e6   0x00000020   Code   RO         1645    .text               mc_w.l(llushr.o)
    0x08000506   0x08000506   0x00000024   Code   RO         1647    .text               mc_w.l(llsshr.o)
    0x0800052a   0x0800052a   0x00000000   Code   RO         1649    .text               mc_w.l(iusefp.o)
    0x0800052a   0x0800052a   0x0000006e   Code   RO         1650    .text               mf_w.l(fepilogue.o)
    0x08000598   0x08000598   0x000000ba   Code   RO         1652    .text               mf_w.l(depilogue.o)
    0x08000652   0x08000652   0x00000002   PAD
    0x08000654   0x08000654   0x00000024   Code   RO         1660    .text               mc_w.l(init.o)
    0x08000678   0x08000678   0x00000016   Code   RO         1130    i.ADC_Cmd           stm32f10x_adc.o
    0x0800068e   0x0800068e   0x00000014   Code   RO         1138    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x080006a2   0x080006a2   0x00000008   Code   RO         1139    i.ADC_GetConversionValue  stm32f10x_adc.o
    0x080006aa   0x080006aa   0x00000012   Code   RO         1141    i.ADC_GetFlagStatus  stm32f10x_adc.o
    0x080006bc   0x080006bc   0x00000014   Code   RO         1144    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x080006d0   0x080006d0   0x00000034   Code   RO          559    i.ADC_GetValue      adcx.o
    0x08000704   0x08000704   0x00000050   Code   RO         1148    i.ADC_Init          stm32f10x_adc.o
    0x08000754   0x08000754   0x000000b8   Code   RO         1152    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x0800080c   0x0800080c   0x0000000a   Code   RO         1153    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x08000816   0x08000816   0x00000016   Code   RO         1155    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x0800082c   0x0800082c   0x0000000a   Code   RO         1157    i.ADC_StartCalibration  stm32f10x_adc.o
    0x08000836   0x08000836   0x00000002   PAD
    0x08000838   0x08000838   0x0000006c   Code   RO          560    i.ADCx_Init         adcx.o
    0x080008a4   0x080008a4   0x00000004   Code   RO            1    i.BusFault_Handler  stm32f10x_it.o
    0x080008a8   0x080008a8   0x00000002   Code   RO            2    i.DebugMon_Handler  stm32f10x_it.o
    0x080008aa   0x080008aa   0x00000116   Code   RO          816    i.GPIO_Init         stm32f10x_gpio.o
    0x080009c0   0x080009c0   0x00000012   Code   RO          822    i.GPIO_ReadOutputDataBit  stm32f10x_gpio.o
    0x080009d2   0x080009d2   0x00000004   Code   RO          823    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080009d6   0x080009d6   0x00000004   Code   RO          824    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080009da   0x080009da   0x0000000a   Code   RO          827    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x080009e4   0x080009e4   0x00000004   Code   RO            3    i.HardFault_Handler  stm32f10x_it.o
    0x080009e8   0x080009e8   0x00000010   Code   RO          295    i.IIC_delay         oled.o
    0x080009f8   0x080009f8   0x00000034   Code   RO          233    i.LED_Init          led.o
    0x08000a2c   0x08000a2c   0x00000014   Code   RO          235    i.LED_On            led.o
    0x08000a40   0x08000a40   0x00000024   Code   RO          236    i.LED_Toggle        led.o
    0x08000a64   0x08000a64   0x00000004   Code   RO            4    i.MemManage_Handler  stm32f10x_it.o
    0x08000a68   0x08000a68   0x00000002   Code   RO            5    i.NMI_Handler       stm32f10x_it.o
    0x08000a6a   0x08000a6a   0x00000002   PAD
    0x08000a6c   0x08000a6c   0x00000030   Code   RO          296    i.OLED_Clear        oled.o
    0x08000a9c   0x08000a9c   0x00000078   Code   RO          303    i.OLED_DrawPoint    oled.o
    0x08000b14   0x08000b14   0x0000003c   Code   RO          304    i.OLED_I2C_Start    oled.o
    0x08000b50   0x08000b50   0x0000002c   Code   RO          305    i.OLED_I2C_Stop     oled.o
    0x08000b7c   0x08000b7c   0x00000034   Code   RO          306    i.OLED_I2C_WaitAck  oled.o
    0x08000bb0   0x08000bb0   0x0000012c   Code   RO          307    i.OLED_Init         oled.o
    0x08000cdc   0x08000cdc   0x00000016   Code   RO          308    i.OLED_Pow          oled.o
    0x08000cf2   0x08000cf2   0x00000002   PAD
    0x08000cf4   0x08000cf4   0x00000068   Code   RO          309    i.OLED_Refresh      oled.o
    0x08000d5c   0x08000d5c   0x00000054   Code   RO          311    i.OLED_Send_Byte    oled.o
    0x08000db0   0x08000db0   0x00000140   Code   RO          312    i.OLED_ShowChar     oled.o
    0x08000ef0   0x08000ef0   0x00000108   Code   RO          313    i.OLED_ShowChinese  oled.o
    0x08000ff8   0x08000ff8   0x00000094   Code   RO          314    i.OLED_ShowNum      oled.o
    0x0800108c   0x0800108c   0x00000038   Code   RO          317    i.OLED_WR_Byte      oled.o
    0x080010c4   0x080010c4   0x0000000c   Code   RO          450    i.PM25_ADC_Read     pm25.o
    0x080010d0   0x080010d0   0x000000c0   Code   RO          451    i.PM25_GetData      pm25.o
    0x08001190   0x08001190   0x0000003c   Code   RO          452    i.PM25_Init         pm25.o
    0x080011cc   0x080011cc   0x00000002   Code   RO            6    i.PendSV_Handler    stm32f10x_it.o
    0x080011ce   0x080011ce   0x00000002   PAD
    0x080011d0   0x080011d0   0x00000018   Code   RO          924    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x080011e8   0x080011e8   0x00000020   Code   RO          928    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001208   0x08001208   0x000000d4   Code   RO          936    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080012dc   0x080012dc   0x00000002   Code   RO            7    i.SVC_Handler       stm32f10x_it.o
    0x080012de   0x080012de   0x00000008   Code   RO          152    i.SetSysClock       system_stm32f10x.o
    0x080012e6   0x080012e6   0x00000002   PAD
    0x080012e8   0x080012e8   0x000000e0   Code   RO          153    i.SetSysClockTo72   system_stm32f10x.o
    0x080013c8   0x080013c8   0x00000028   Code   RO          598    i.SysTick_CLKSourceConfig  misc.o
    0x080013f0   0x080013f0   0x00000002   Code   RO            8    i.SysTick_Handler   stm32f10x_it.o
    0x080013f2   0x080013f2   0x00000002   PAD
    0x080013f4   0x080013f4   0x00000060   Code   RO          155    i.SystemInit        system_stm32f10x.o
    0x08001454   0x08001454   0x0000007c   Code   RO          505    i.USART1_Config     usart.o
    0x080014d0   0x080014d0   0x00000018   Code   RO          634    i.USART_Cmd         stm32f10x_usart.o
    0x080014e8   0x080014e8   0x000000d8   Code   RO          641    i.USART_Init        stm32f10x_usart.o
    0x080015c0   0x080015c0   0x00000008   Code   RO          651    i.USART_SendData    stm32f10x_usart.o
    0x080015c8   0x080015c8   0x00000004   Code   RO            9    i.UsageFault_Handler  stm32f10x_it.o
    0x080015cc   0x080015cc   0x00000020   Code   RO         1394    i.__0printf$1       mc_w.l(printf1.o)
    0x080015ec   0x080015ec   0x0000000e   Code   RO         1664    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080015fa   0x080015fa   0x00000002   Code   RO         1665    i.__scatterload_null  mc_w.l(handlers.o)
    0x080015fc   0x080015fc   0x0000000e   Code   RO         1666    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800160a   0x0800160a   0x00000002   PAD
    0x0800160c   0x0800160c   0x00000154   Code   RO         1401    i._printf_core      mc_w.l(printf1.o)
    0x08001760   0x08001760   0x0000003c   Code   RO          479    i.delay_init        delay.o
    0x0800179c   0x0800179c   0x0000003c   Code   RO          480    i.delay_ms          delay.o
    0x080017d8   0x080017d8   0x0000003c   Code   RO          481    i.delay_us          delay.o
    0x08001814   0x08001814   0x00000024   Code   RO          506    i.fputc             usart.o
    0x08001838   0x08001838   0x000000d4   Code   RO          186    i.main              main.o
    0x0800190c   0x0800190c   0x00001d30   Data   RO          319    .constdata          oled.o
    0x0800363c   0x0800363c   0x00000020   Data   RO         1662    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800365c, Size: 0x000006a0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800365c   0x00000002   Data   RW          187    .data               main.o
    0x20000002   0x0800365e   0x00000004   Data   RW          482    .data               delay.o
    0x20000006   0x08003662   0x00000014   Data   RW          956    .data               stm32f10x_rcc.o
    0x2000001a   0x08003676   0x00000002   PAD
    0x2000001c   0x08003678   0x00000004   Data   RW         1638    .data               mc_w.l(stdout.o)
    0x20000020        -       0x00000480   Zero   RW          318    .bss                oled.o
    0x200004a0        -       0x00000200   Zero   RW          587    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       160          8          0          0          0       1247   adcx.o
       180         16          0          4          0       1939   delay.o
       108         14          0          0          0     212321   led.o
       212         30          0          2          0        800   main.o
        40          0          0          0          0        566   misc.o
      1638         76       7472          0       1152      11166   oled.o
       264         34          0          0          0       1731   pm25.o
        28          4        236          0        512        820   startup_stm32f10x_md.o
       394         10          0          0          0       8513   stm32f10x_adc.o
       314          0          0          0          0       4505   stm32f10x_gpio.o
        26          0          0          0          0     233614   stm32f10x_it.o
       268         32          0         20          0       4628   stm32f10x_rcc.o
       248          6          0          0          0       3562   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       328         28          0          0          0       1977   system_stm32f10x.o
       160         14          0          0          0       3020   usart.o

    ----------------------------------------------------------------------
      4380        <USER>       <GROUP>         28       1664     490441   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       372         14          0          0          0        184   printf1.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
       334          0          0          0          0        148   dadd.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      1796         <USER>          <GROUP>          4          0       1504   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       600         30          0          4          0        536   mc_w.l
      1192          0          0          0          0        968   mf_w.l

    ----------------------------------------------------------------------
      1796         <USER>          <GROUP>          4          0       1504   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      6176        302       7740         32       1664     488353   Grand Totals
      6176        302       7740         32       1664     488353   ELF Image Totals
      6176        302       7740         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13916 (  13.59kB)
    Total RW  Size (RW Data + ZI Data)              1696 (   1.66kB)
    Total ROM Size (Code + RO Data + RW Data)      13948 (  13.62kB)

==============================================================================

